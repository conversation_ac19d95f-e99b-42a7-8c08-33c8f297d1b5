import mongoose, { Document, Schema } from 'mongoose';
import { User, UserRole } from '../../../shared/types';

export interface UserDocument extends User, Document {}

const UserSchema = new Schema<UserDocument>(
  {
    firebaseUid: {
      type: String,
      required: true,
      unique: true,
      index: true,
    },
    email: {
      type: String,
      required: true,
      unique: true,
      lowercase: true,
      index: true,
    },
    displayName: {
      type: String,
      required: true,
    },
    photoURL: {
      type: String,
    },
    role: {
      type: String,
      enum: ['student', 'tutor', 'admin'],
      required: true,
      default: 'student',
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    // Tutor specific fields
    approvedBy: {
      type: String, // Admin Firebase UID
    },
    approvalDate: {
      type: Date,
    },
    // Student specific fields
    subscriptions: [{
      type: Schema.Types.ObjectId,
      ref: 'Subscription',
    }],
  },
  {
    timestamps: true,
    toJSON: {
      transform: function(doc, ret) {
        delete ret.__v;
        return ret;
      }
    }
  }
);

// Indexes
UserSchema.index({ firebaseUid: 1 });
UserSchema.index({ email: 1 });
UserSchema.index({ role: 1 });
UserSchema.index({ isActive: 1 });

// Middleware to ensure tutors have approval info
UserSchema.pre('save', function(next) {
  if (this.role === 'tutor' && this.isActive && !this.approvedBy) {
    const error = new Error('Tutors must be approved by an admin');
    return next(error);
  }
  next();
});

export const UserModel = mongoose.model<UserDocument>('User', UserSchema);