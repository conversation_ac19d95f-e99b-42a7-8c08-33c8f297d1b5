import express from 'express';
import { AssignmentController } from '../controllers/assignment.controller';
import { authenticateToken, requireRole } from '../../../shared/middleware/auth';

const router = express.Router();
const assignmentController = new AssignmentController();

// Student routes
router.post('/submit', authenticateToken, requireRole(['student']), assignmentController.submitAssignment);
router.get('/:assignmentId/result', authenticateToken, requireRole(['student']), assignmentController.getAssignmentResult);

// Tutor routes for assignment management
router.post('/:courseId/modules/:moduleId/lessons/:lessonId/assignments', authenticateToken, requireRole(['tutor', 'admin']), assignmentController.createAssignment);
router.put('/:courseId/modules/:moduleId/lessons/:lessonId/assignments/:assignmentId', authenticateToken, requireRole(['tutor', 'admin']), assignmentController.updateAssignment);
router.delete('/:courseId/modules/:moduleId/lessons/:lessonId/assignments/:assignmentId', authenticateToken, requireRole(['tutor', 'admin']), assignmentController.deleteAssignment);

export default router;