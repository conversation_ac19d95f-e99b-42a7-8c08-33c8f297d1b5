import express from 'express';
import { CoachingController } from '../controllers/coaching.controller';
import { authenticateToken, requireRole } from '../../../shared/middleware/auth';

const router = express.Router();
const coachingController = new CoachingController();

// Tutor routes
router.post('/', authenticateToken, requireRole(['tutor']), coachingController.createCoachingSession);
router.get('/my-sessions', authenticateToken, requireRole(['tutor']), coachingController.getMyCoachingSessions);
router.put('/:sessionId', authenticateToken, requireRole(['tutor']), coachingController.updateCoachingSession);
router.delete('/:sessionId', authenticateToken, requireRole(['tutor']), coachingController.deleteCoachingSession);

// Student routes
router.get('/available', authenticateToken, requireRole(['student']), coachingController.getAvailableCoachingSessions);
router.post('/:sessionId/join', authenticateToken, requireRole(['student']), coachingController.joinCoachingSession);

export default router;