import express from 'express';
import { LessonController } from '../controllers/lesson.controller';
import { authenticateToken, requireRole } from '../../../shared/middleware/auth';

const router = express.Router();
const lessonController = new LessonController();

// Tutor routes for lesson management
router.post('/:courseId/modules/:moduleId/lessons', authenticateToken, requireRole(['tutor', 'admin']), lessonController.createLesson);
router.put('/:courseId/modules/:moduleId/lessons/:lessonId', authenticateToken, requireRole(['tutor', 'admin']), lessonController.updateLesson);
router.delete('/:courseId/modules/:moduleId/lessons/:lessonId', authenticateToken, requireRole(['tutor', 'admin']), lessonController.deleteLesson);

// Student routes
router.get('/:courseId/modules/:moduleId/lessons/:lessonId', authenticateToken, lessonController.getLesson);

// Video upload routes
router.post('/:courseId/modules/:moduleId/lessons/:lessonId/upload-video', authenticateToken, requireRole(['tutor', 'admin']), lessonController.uploadVideo);

export default router;