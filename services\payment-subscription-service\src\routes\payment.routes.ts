import express from 'express';
import { PaymentController } from '../controllers/payment.controller';
import { authenticateToken, requireRole } from '../../../shared/middleware/auth';

const router = express.Router();
const paymentController = new PaymentController();

// Student routes
router.post('/create', authenticateToken, requireRole(['student']), paymentController.createPayment);
router.get('/my-payments', authenticateToken, requireRole(['student']), paymentController.getMyPayments);
router.get('/:paymentId', authenticateToken, requireRole(['student']), paymentController.getPaymentById);

// Admin routes
router.get('/admin/all', authenticateToken, requireRole(['admin']), paymentController.getAllPayments);

export default router;