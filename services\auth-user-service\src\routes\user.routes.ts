import express from 'express';
import { UserController } from '../controllers/user.controller';
import { authenticateToken, requireRole } from '../../../shared/middleware/auth';

const router = express.Router();
const userController = new UserController();

// Admin only routes
router.get('/', authenticateToken, requireRole(['admin']), userController.getAllUsers);
router.get('/:id', authenticateToken, requireRole(['admin']), userController.getUserById);
router.put('/:id', authenticateToken, requireRole(['admin']), userController.updateUser);
router.delete('/:id', authenticateToken, requireRole(['admin']), userController.deleteUser);

// Student specific routes
router.get('/students/failed', authenticateToken, requireRole(['admin', 'tutor']), userController.getFailedStudents);

export default router;